# PyBOP 完整使用指南

## 目录
1. [简介](#简介)
2. [安装](#安装)
3. [核心概念](#核心概念)
4. [快速开始](#快速开始)
5. [模型类型](#模型类型)
6. [参数设置](#参数设置)
7. [成本函数](#成本函数)
8. [优化器](#优化器)
9. [问题定义](#问题定义)
10. [采样器](#采样器)
11. [观测器](#观测器)
12. [可视化](#可视化)
13. [应用实例](#应用实例)
14. [高级功能](#高级功能)

## 简介

PyBOP (Python Battery Optimization and Parameterization) 是一个专门用于电池模型优化和参数化的 Python 库。它基于 PyBaMM 构建，提供了完整的工具集用于电池模型的参数推断、优化和不确定性量化。

### 主要特性
- **参数优化**：支持确定性和随机性优化方法
- **贝叶斯推断**：提供贝叶斯参数估计和不确定性量化
- **多种模型**：支持电化学模型和等效电路模型
- **丰富的优化器**：集成多种优化算法
- **可视化工具**：提供专业的结果可视化功能

## 安装

### 基本安装
```bash
pip install pybop
```

### 从源码安装
```bash
git clone https://github.com/pybop-team/PyBOP.git
cd PyBOP
pip install -e .
```

### 依赖要求
- Python >= 3.8
- PyBaMM
- NumPy
- SciPy
- Matplotlib
- PINTS (用于优化和采样)

## 核心概念

### 基本组件
- **Model**: 电池模型（电化学或等效电路）
- **Parameters**: 待优化的参数集合
- **Dataset**: 实验数据
- **Cost Function**: 成本函数（目标函数）
- **Optimiser**: 优化算法
- **Problem**: 优化问题定义

### 工作流程
1. 定义电池模型
2. 设置待优化参数
3. 加载实验数据
4. 选择成本函数
5. 配置优化器
6. 运行优化
7. 分析结果

## 快速开始

### 基本参数优化示例

```python
import pybop
import numpy as np

# 1. 创建模型
model = pybop.lithium_ion.SPM()

# 2. 定义参数
parameters = pybop.Parameters([
    pybop.Parameter(
        "Negative electrode active material volume fraction",
        prior=pybop.Uniform(0.4, 0.7),
        bounds=[0.375, 0.75]
    ),
    pybop.Parameter(
        "Positive electrode active material volume fraction", 
        prior=pybop.Uniform(0.5, 0.8),
        bounds=[0.48, 0.78]
    )
])

# 3. 生成或加载数据
# 这里使用合成数据作为示例
experiment = pybop.Experiment([
    "Discharge at 1C for 0.5 hours",
    "Rest for 0.5 hours"
])

# 4. 定义成本函数
cost = pybop.SumSquaredError()

# 5. 创建优化问题
problem = pybop.FittingProblem(
    model=model,
    parameters=parameters,
    dataset=dataset,  # 需要提供实际数据
    cost=cost
)

# 6. 选择优化器
optimiser = pybop.CMAES(
    cost=problem,
    max_iterations=100
)

# 7. 运行优化
results = optimiser.run()

# 8. 查看结果
print("Optimized parameters:")
for i, param in enumerate(parameters):
    print(f"{param.name}: {results.x[i]:.4f}")
```

## 模型类型

### 电化学模型

#### 单颗粒模型 (SPM)
```python
# 基本 SPM 模型
model = pybop.lithium_ion.SPM()

# 带电解质的 SPM 模型
model = pybop.lithium_ion.SPMe()

# 带扩散的单颗粒模型
model = pybop.lithium_ion.SPDiffusion()
```

#### 电化学模型
```python
# 完整电化学模型
model = pybop.lithium_ion.DFN()

# Weppner-Huggins 模型
model = pybop.lithium_ion.WeppnerHuggins()
```

### 等效电路模型

#### 基本 ECM 模型
```python
# Thevenin 等效电路模型
model = pybop.empirical.Thevenin()

# 双 RC 等效电路模型
model = pybop.empirical.DualRC()

# 自定义 ECM 模型
model = pybop.empirical.ECM(
    R0=0.01,  # 欧姆电阻
    R1=0.02,  # 极化电阻1
    C1=1000,  # 极化电容1
    R2=0.03,  # 极化电阻2
    C2=5000   # 极化电容2
)
```

### 经验模型
```python
# 指数衰减模型
model = pybop.ExponentialDecay()
```

## 参数设置

### 参数定义

#### 基本参数
```python
# 单个参数
param = pybop.Parameter(
    name="Negative electrode active material volume fraction",
    prior=pybop.Uniform(0.4, 0.7),  # 先验分布
    bounds=[0.375, 0.75],           # 边界约束
    initial_value=0.55              # 初始值
)

# 参数集合
parameters = pybop.Parameters([
    pybop.Parameter(
        "Negative electrode active material volume fraction",
        prior=pybop.Uniform(0.4, 0.7),
        bounds=[0.375, 0.75]
    ),
    pybop.Parameter(
        "Positive electrode active material volume fraction",
        prior=pybop.Uniform(0.5, 0.8), 
        bounds=[0.48, 0.78]
    ),
    pybop.Parameter(
        "Negative particle radius [m]",
        prior=pybop.Uniform(5e-6, 15e-6),
        bounds=[1e-6, 20e-6]
    )
])
```

#### 先验分布
```python
# 均匀分布
uniform_prior = pybop.Uniform(lower=0.1, upper=0.9)

# 正态分布
normal_prior = pybop.Gaussian(mean=0.5, sigma=0.1)

# 对数正态分布
lognormal_prior = pybop.LogNormal(mean=0.0, sigma=1.0)

# 指数分布
exponential_prior = pybop.Exponential(scale=1.0)
```

### 参数变换
```python
# 对数变换（用于正值参数）
param = pybop.Parameter(
    "Diffusivity [m2.s-1]",
    prior=pybop.Uniform(1e-15, 1e-13),
    bounds=[1e-16, 1e-12],
    transformation=pybop.LogTransformation()
)
```

## 成本函数

### 误差度量

#### 平方误差
```python
# 平方和误差
cost = pybop.SumSquaredError()

# 均方误差
cost = pybop.MeanSquaredError()

# 均方根误差
cost = pybop.RootMeanSquaredError()
```

#### 绝对误差
```python
# 平均绝对误差
cost = pybop.MeanAbsoluteError()

# 平均绝对百分比误差
cost = pybop.MeanAbsolutePercentageError()
```

### 似然函数
```python
# 高斯似然
cost = pybop.GaussianLogLikelihood()

# 高斯似然（已知噪声）
cost = pybop.GaussianLogLikelihoodKnownSigma(sigma=0.01)
```

### 设计成本
```python
# 重力能量密度
cost = pybop.GravimetricEnergyDensity()

# 体积能量密度  
cost = pybop.VolumetricEnergyDensity()
```

### 加权成本
```python
# 多目标优化
cost1 = pybop.SumSquaredError()
cost2 = pybop.GravimetricEnergyDensity()

weighted_cost = pybop.WeightedCost(
    costs=[cost1, cost2],
    weights=[0.7, 0.3]
)
```

## 优化器

### 基于梯度的优化器

#### SciPy 优化器
```python
# L-BFGS-B
optimiser = pybop.SciPyMinimize(
    method="L-BFGS-B",
    max_iterations=1000
)

# Nelder-Mead
optimiser = pybop.SciPyMinimize(
    method="Nelder-Mead",
    max_iterations=1000
)
```

#### 自定义梯度优化器
```python
# Adam 优化器
optimiser = pybop.Adam(
    learning_rate=0.01,
    max_iterations=1000
)

# 梯度下降
optimiser = pybop.GradientDescent(
    learning_rate=0.01,
    max_iterations=1000
)

# IRProp+
optimiser = pybop.IRPropPlus(
    max_iterations=1000
)
```

### 无梯度优化器

#### 进化算法
```python
# CMA-ES (协方差矩阵自适应进化策略)
optimiser = pybop.CMAES(
    population_size=20,
    max_iterations=100
)

# 差分进化
optimiser = pybop.DifferentialEvolution(
    population_size=30,
    max_iterations=100
)
```

#### 群体智能算法
```python
# 粒子群优化
optimiser = pybop.PSO(
    population_size=20,
    max_iterations=100
)

# 布谷鸟搜索
optimiser = pybop.CuckooSearch(
    population_size=20,
    max_iterations=100
)
```

#### 其他算法
```python
# 模拟退火
optimiser = pybop.SimulatedAnnealing(
    max_iterations=1000,
    initial_temperature=1.0
)

# 随机搜索
optimiser = pybop.RandomSearch(
    max_iterations=1000
)
```

### 优化器配置
```python
# 详细配置示例
optimiser = pybop.CMAES(
    cost=problem,
    population_size=20,
    max_iterations=100,
    max_unchanged_iterations=20,
    absolute_tolerance=1e-6,
    relative_tolerance=1e-6,
    sigma0=0.1,  # 初始步长
    seed=42      # 随机种子
)
```

## 问题定义

### 拟合问题

#### 基本拟合问题
```python
# 单一数据集拟合
problem = pybop.FittingProblem(
    model=model,
    parameters=parameters,
    dataset=dataset,
    cost=cost,
    init_soc=0.5  # 初始荷电状态
)

# 多数据集拟合
problem = pybop.MultiFittingProblem(
    model=model,
    parameters=parameters,
    datasets=[dataset1, dataset2, dataset3],
    cost=cost
)
```

#### 设计问题
```python
# 电池设计优化
design_parameters = pybop.Parameters([
    pybop.Parameter(
        "Negative electrode thickness [m]",
        prior=pybop.Uniform(50e-6, 150e-6),
        bounds=[20e-6, 200e-6]
    ),
    pybop.Parameter(
        "Positive electrode thickness [m]",
        prior=pybop.Uniform(50e-6, 150e-6),
        bounds=[20e-6, 200e-6]
    )
])

design_problem = pybop.DesignProblem(
    model=model,
    parameters=design_parameters,
    experiment=experiment,
    cost=pybop.GravimetricEnergyDensity()
)
```

### 数据集处理

#### 加载实验数据
```python
# 从文件加载数据
dataset = pybop.Dataset({
    "Time [s]": time_data,
    "Current [A]": current_data,
    "Voltage [V]": voltage_data,
    "Temperature [K]": temperature_data
})

# 从 CSV 文件加载
dataset = pybop.Dataset.from_csv("battery_data.csv")

# 数据预处理
dataset = dataset.resample(dt=1.0)  # 重采样到1秒间隔
dataset = dataset.filter(variables=["Time [s]", "Voltage [V]"])  # 筛选变量
```

#### 合成数据生成
```python
# 生成合成数据用于测试
true_parameters = [0.6, 0.65, 10e-6]
synthetic_data = pybop.generate_synthetic_data(
    model=model,
    parameters=parameters,
    parameter_values=true_parameters,
    experiment=experiment,
    noise_level=0.01
)
```

## 采样器

### MCMC 采样

#### 基本 MCMC 采样
```python
# Metropolis-Hastings 采样器
sampler = pybop.MetropolisRandomWalkMCMC(
    log_pdf=problem,
    chains=4,
    max_iterations=10000
)

# 自适应协方差 MCMC (Haario Bardenet)
sampler = pybop.HaarioBardenetMCMC(
    log_pdf=problem,
    chains=4,
    max_iterations=10000
)

# No-U-Turn 采样器
sampler = pybop.NoUTurnMCMC(
    log_pdf=problem,
    chains=4,
    max_iterations=10000
)

# Hamiltonian Monte Carlo
sampler = pybop.HamiltonianMCMC(
    log_pdf=problem,
    chains=4,
    max_iterations=10000
)
```

#### 运行采样
```python
# 运行 MCMC 采样
chains = sampler.run()

# 处理采样结果
processor = pybop.ChainProcessor(
    chains=chains,
    parameter_names=[p.name for p in parameters]
)

# 计算统计量
summary = processor.get_summary()
print("Parameter estimates:")
for param, stats in summary.items():
    print(f"{param}: {stats['mean']:.4f} ± {stats['std']:.4f}")
```

### 采样诊断
```python
# 收敛诊断
r_hat = processor.r_hat()  # Gelman-Rubin 统计量
ess = processor.effective_sample_size()  # 有效样本数

print("Convergence diagnostics:")
for i, param in enumerate(parameters):
    print(f"{param.name}: R-hat = {r_hat[i]:.3f}, ESS = {ess[i]:.0f}")

# 自相关分析
autocorr = processor.autocorrelation()
```

## 观测器

### 状态估计

#### 无迹卡尔曼滤波器
```python
# UKF 观测器
observer = pybop.UnscentedKalmanFilter(
    model=model,
    parameters=parameters,
    dataset=dataset,
    signal=["Voltage [V]"],
    n_states=2,  # 状态数量
    process_noise=0.01,
    measurement_noise=0.001
)

# 运行状态估计
states = observer.run()

# 提取状态估计结果
soc_estimate = states["State of Charge"]
temperature_estimate = states["Temperature [K]"]
```

#### 扩展卡尔曼滤波器
```python
# EKF 观测器
observer = pybop.ExtendedKalmanFilter(
    model=model,
    parameters=parameters,
    dataset=dataset,
    signal=["Voltage [V]"],
    n_states=2
)
```
