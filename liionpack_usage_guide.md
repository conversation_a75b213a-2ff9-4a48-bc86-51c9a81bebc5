# liionpack 完整使用指南

## 目录
1. [简介](#简介)
2. [安装](#安装)
3. [基础概念](#基础概念)
4. [快速开始](#快速开始)
5. [网络拓扑设计](#网络拓扑设计)
6. [参数设置](#参数设置)
7. [实验设计](#实验设计)
8. [初始状态设置](#初始状态设置)
9. [驱动循环仿真](#驱动循环仿真)
10. [模型选择](#模型选择)
11. [可视化功能](#可视化功能)
12. [SEI退化模型](#sei退化模型)
13. [端子位置设置](#端子位置设置)
14. [高级功能](#高级功能)

## 简介

liionpack 是一个基于 PyBaMM 的 Python 库，专门用于锂离子电池包的建模和仿真。它允许用户创建复杂的电池包网络拓扑，进行电化学仿真，并分析电池包的性能。

## 安装

```bash
pip install liionpack
```

或者从源码安装：
```bash
git clone https://github.com/pybamm-team/liionpack.git
cd liionpack
pip install -e .
```

## 基础概念

### 核心组件
- **Netlist**: 定义电池包的网络拓扑结构
- **Parameter Values**: 电池的物理和化学参数
- **Experiment**: 定义充放电协议
- **Model**: 电化学模型选择
- **Solver**: 求解器配置

### 坐标系统
- **Np**: 并联电池数量
- **Ns**: 串联电池数量
- **I**: 电流方向（正值为放电）
- **V**: 电压

## 快速开始

### 基本示例

```python
import liionpack as lp
import pybamm
import numpy as np

# 1. 创建网络拓扑 - 16个并联，2个串联的电池包
netlist = lp.setup_circuit(Np=16, Ns=2, Rb=1e-4, Rc=1e-2)

# 2. 设置电池参数
parameter_values = pybamm.ParameterValues("Chen2020")

# 3. 定义实验协议
experiment = pybamm.Experiment([
    "Discharge at 5 A for 1 hour",
    "Rest for 30 minutes",
    "Charge at 1 A until 4.1 V",
    "Hold at 4.1 V until 50 mA"
], period="10 seconds")

# 4. 运行仿真
output = lp.solve(
    netlist=netlist,
    parameter_values=parameter_values,
    experiment=experiment,
    output_variables=None,
    initial_soc=0.5
)

# 5. 可视化结果
lp.plot_output(output)
```

### 关键参数说明
- `Np`: 并联电池数量
- `Ns`: 串联电池数量
- `Rb`: 母线电阻 (Ω)
- `Rc`: 连接电阻 (Ω)
- `period`: 数据记录间隔
