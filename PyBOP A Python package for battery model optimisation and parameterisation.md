# PyBOP: A Python package for battery model optimisation and parameterisation

**<PERSON><sup>1</sup>, <PERSON><sup>1,2</sup>, <PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><PERSON><sup>2,5</sup>, and <PERSON><sup>1,2</sup>**

1.  *Department of Engineering Science, University of Oxford, Oxford, UK*
2.  *The Faraday Institution, Harwell Campus, Didcot, UK*
3.  *Research Software Engineering Group, University of Oxford, Oxford, UK*
4.  *Quansight Labs*
5.  *Mathematics Institute, University of Warwick, Coventry, UK*

20 December 2024

## Summary

The Python Battery Optimisation and Parameterisation (PyBOP) package provides methods for estimating and optimising battery model parameters, offering both deterministic and stochastic approaches with example workflows to assist users. PyBOP enables parameter identification from data for various battery models, including the electrochemical and equivalent circuit models provided by the popular open-source PyBaMM package (<PERSON><PERSON> et al., 2021). Using the same approaches, PyBOP can also be used for design optimisation under user-defined operating conditions across a variety of model structures and design goals. PyBOP facilitates optimisation with a range of methods, with diagnostics for examining optimiser performance and convergence of the cost and corresponding parameters. Identified parameters can be used for prediction, on-line estimation and control, and design optimisation, accelerating battery research and development.

## Statement of need

PyBOP is a Python package providing a user-friendly, object-oriented interface for optimising battery model parameters. PyBOP leverages the open-source PyBaMM package (Sulzer et al., 2021) to formulate and solve battery models. Together, these tools serve a broad audience including students, engineers, and researchers in academia and industry, enabling the use of advanced models where previously this was not possible without specialised knowledge of battery modelling, parameter inference, and software development. PyBOP emphasises clear and informative diagnostics and workflows to support users with varying levels of domain expertise, and provides access to a wide range of optimisation and sampling algorithms. These are enabled through interfaces to PINTS (Clerx et al., 2019), SciPy (Virtanen et al., 2020), and PyBOP's own implementations of algorithms such as adaptive moment estimation with weight decay (AdamW) (Loshchilov & Hutter, 2017), gradient descent (Cauchy & others, 1847), and cuckoo search (Yang & Suash Deb, 2009).

PyBOP supports the battery parameter exchange (BPX) standard (Korotkin et al., 2023) for sharing parameter sets. These are typically costly to obtain due to the specialised equipment and time required for characterisation experiments, the need for domain knowledge, and the computational cost of estimation. PyBOP reduces the requirements for the latter two by providing fast parameter estimation methods, standardised workflows, and parameter set interoperability (via BPX). PyBOP complements other lithium-ion battery modelling packages built around PyBaMM, such as liionpack for battery pack simulation (Tranter et al., 2022) and pybamm-eis for fast numerical computation of the electrochemical impedance of any battery model. Identified PyBOP parameters are easily exportable to other packages.

## Architecture

PyBOP has a layered structure enabling the necessary functionality to compute forward predictions, process results, and run optimisation and sampling algorithms. The forward model is solved using the battery modelling software PyBaMM, with construction, parameterisation, and discretisation managed by PyBOP's model interface to PyBaMM. This provides a robust object construction process with a consistent interface between forward models and optimisers. Furthermore, identifiability metrics are provided along with the estimated parameters (through Hessian approximation of the cost functions around the optimum point in frequentist workflows, and posterior distributions in Bayesian workflows).

***

**Figure 1: The core PyBOP architecture with base class interfaces. Each class provides a direct mapping to a step in the optimisation workflow.**

*A textual representation of the workflow diagram:*
```
Main optimisation workflow:

[Equations + solver] -> [Model] -> [Problem] <- [Input/Dataset]
[Fixed parameters] -> [Model]
[Uncertain parameters] -> [Problem]
[Cost function] -> [Cost] -> [Optimiser] <- [Numerical method]

- Model (Class): predict/simulate - returns a solution dictionary
- Problem (Class): evaluate - returns a solution dictionary
- Cost (Class): call - returns the cost value
- Optimiser (Class): run - returns parameter values and final cost
```
***

PyBOP formulates the inference process into four key classes: model, problem, cost (or likelihood), and optimiser (or sampler), as shown in Figure 1. Each of these objects represents a base class with child classes constructing specialised functionality for different workflows. The model class constructs a PyBaMM forward model with a specified set of equations, initial conditions, spatial discretisation, and numerical solver. By composing PyBaMM directly into PyBOP, specialised models can be constructed alongside the standard models that can also be modified for different inference tasks. One such example is spatial re-discretisation, which is required when one or more geometric parameters are being optimised. In this situation, PyBOP rebuilds the PyBaMM model only when necessary, reducing the total number of rebuilds, providing improved performance. Alongside construction of the forward model, PyBOP's model class provides methods for obtaining sensitivities from the prediction, enabling gradient-based optimisation. A forward prediction, along with its corresponding sensitivities, is provided to the problem class for processing and exception control. A standardised data structure is then provided to the cost classes, which computes a distance, design, or likelihood-based metric for optimisation. For point-based optimisation, the optimisers minimise the cost function or the negative log-likelihood if a likelihood class is provided. Bayesian inference is provided by sampler classes, which accept the LogPosterior class and sample from it using PINTS-based Monte Carlo algorithms at the time of submission. In the typical workflow, the classes in Figure 1 are constructed in sequence, from left to right in the figure.

In addition to the core architecture, PyBOP provides several specialised inference and optimisation features. One example is parameter inference from electrochemical impedance spectroscopy (EIS) simulations, where PyBOP discretises and linearises the EIS forward model into a sparse mass matrix form with accompanying auto-differentiated Jacobian. This is then translated into the frequency domain, giving a direct solution to compute the input-output impedance. In this situation, the forward models are constructed within the spatial re-discretisation workflow, allowing for geometric parameter inference from EIS simulations and data.

A second specialised feature is that PyBOP builds on the JAX (Bradbury et al., 2018) numerical solvers used by PyBaMM by providing JAX-based cost functions for automatic forward model differentiation with respect to the parameters. This functionality provides a performance improvement and allows users to harness many other JAX-based inference packages to optimise cost functions, such as NumPyro (Phan et al., 2019), BlackJAX (Cabezas et al., 2024), and Optax (DeepMind et al., 2020).

The currently implemented subclasses for the model, problem, and cost classes are listed in Table 1. The model and optimiser classes can be selected in combination with any problem-cost pair.

**Table 1: List of available model, problem and cost/likelihood classes.**

| Battery Models                      | Problem Types   | Cost / Likelihood          |
| :---------------------------------- | :-------------- | :------------------------- |
| Single-particle model (SPM)         | Fitting problem | Sum-squared error          |
| SPM with electrolyte (SPMe)         | Design problem  | Root-mean-squared error    |
| Doyle-Fuller-Newman (DFN)           | Observer        | Minkowski                  |
| Many-particle model (MPM)           |                 | Sum-of-power               |
| Multi-species multi-reaction (MSMR) |                 | Gaussian log likelihood    |
| Weppner Huggins                     |                 | Maximum a posteriori       |
| Equivalent circuit model (ECM)      |                 | Volumetric energy density  |
|                                     |                 | Gravimetric energy density |

Similarly, the current algorithms available for optimisation are presented in Table 2. It should be noted that SciPy minimize includes several gradient and non-gradient methods. From here on, the point-based parameterisation and design-optimisation tasks will simply be referred to as optimisation tasks. This simplification can be justified by comparing Equation 5 and Equation 7; deterministic parameterisation is just an optimisation task to minimise a distance-based cost between model output and measured values.

**Table 2: Currently supported optimisers classified by candidate solution type, including gradient information.**

| Gradient-based                                    | Evolutionary                          | (Meta)heuristic      |
| :------------------------------------------------ | :------------------------------------ | :------------------- |
| Weight decayed adaptive moment estimation (AdamW) | Covariance matrix adaptation (CMA-ES) | Particle swarm (PSO) |
| Improved resilient backpropagation (iRProp-)      | Exponential natural (xNES)            | Nelder-Mead          |
| Gradient descent                                  | Separable natural (sNES)              | Cuckoo search        |
| SciPy minimize                                    | Differential evolution                |                      |

In addition to deterministic optimisers Table 1, PyBOP also provides Monte Carlo sampling routines to estimate distributions of parameters within a Bayesian framework. These methods construct a posterior parameter distribution that can be used to assess uncertainty and practical identifiability. The individual sampler classes are currently composed within PyBOP from the PINTS library, with a base sampler class implemented for interoperability and direct integration with PyBOP’s model, problem, and likelihood classes. The currently supported samplers are listed in Table 3.

**Table 3: Sampling methods supported by PyBOP, classified according to the candidate proposal method.**

| Gradient-based | Adaptive                   | Slicing        | Evolutionary           | Other                        |
| :------------- | :------------------------- | :------------- | :--------------------- | :--------------------------- |
| Monomial gamma | Delayed rejection adaptive | Rank shrinking | Differential evolution | Metropolis random walk       |
| No-U-turn      | Haario Bardenet            | Doubling       |                        | Emcee hammer                 |
| Hamiltonian    | Haario                     | Stepout        |                        | Metropolis adjusted Langevin |
| Relativistic   | Rao Blackwell              |                |                        |                              |

## Background

### Battery models

In general, battery models, after spatial discretisation, can be written in the form of a differential-algebraic system of equations,
```
dx/dt = f(t, x, θ), (1)
0 = g(t, x, θ),    (2)
y(t) = h(t, x, θ), (3)
```
with initial conditions
```
x(0) = x₀(θ).      (4)
```
Here, *t* is time, *x(t)* are the spatially discretised states, *y(t)* are the outputs (e.g. the terminal voltage) and *θ* are the unknown parameters.

Common battery models include various types of equivalent circuit models (e.g. the Thévenin model), the Doyle-Fuller-Newman (DFN) model (Doyle et al., 1993; Fuller et al., 1994) based on porous electrode theory, and its reduced-order variants including the single particle model (SPM) (Brosa Planella et al., 2022) and the multi-species multi-reaction (MSMR) model (Verbrugge et al., 2017). Simplified models that retain acceptable predictive accuracy at lower computational cost are widely used, for example in battery management systems, while physics-based models are required to understand the impact of physical parameters on performance. This separation of complexity traditionally results in multiple parameterisations for a single battery type, depending on the model structure.

## Examples

### Parameterisation

The parameterisation of battery models is challenging due to the large number of parameters that need to be identified compared to the number of measurable outputs (Andersson et al., 2022; Miguel et al., 2021; Wang et al., 2022). A complete parameterisation often requires stepwise identification of smaller sets of parameters from a variety of excitations and different data sets (Chen et al., 2020; Chu et al., 2019; Kirk et al., 2023; Lu et al., 2021). Furthermore, parameter identifiability can be poor for a given set of excitations and data sets, requiring improved experimental design in addition to uncertainty capable identification methods (Aitio et al., 2020).

A generic data-fitting optimisation problem may be formulated as:
```
min L(y_i)(θ) subject to equations (1)-(4)    (5)
```
where L: θ → [0, ∞) is a cost function that quantifies the agreement between the model output y(t) and a sequence of observations (yᵢ) measured at times tᵢ. Within the PyBOP framework, the `FittingProblem` class packages the model output along with the measured observations, both of which are then passed to the cost classes for the computation of the specific cost function. For gradient-based optimisers, the Jacobian of the cost function with respect to unknown parameters, ∂L/∂θ, is computed for step-size and directional information.

Next, we demonstrate the fitting of synthetic data where the model parameters are known. Throughout this section, as an example, we use PyBaMM's implementation of the single particle model with an added contact resistance submodel. We assume that the model is already fully parameterised apart from two dynamic parameters, namely, the lithium diffusivity of the negative electrode active material particles (denoted "negative particle diffusivity") and the contact resistance with corresponding true values of [3.3e-14 m²/s, 10 mOhm]. To start, we generate synthetic time-domain data corresponding to a one-hour discharge from 100% to 0% state of charge, denoted as 1C rate, followed by 30 minutes of relaxation. This dataset is then corrupted with zero-mean Gaussian noise of amplitude 2 mV, with the resulting signal shown by the blue dots in Figure 2 (left). The initial states are assumed known, although this assumption is not generally necessary. The PyBOP repository contains several other example notebooks that follow a similar inference process. The underlying cost landscape to be explored by the optimiser is shown in Figure 2 (right), with the initial position denoted alongside the known true system parameters for this synthetic inference task. In general, the true parameters are not known.

***
**Figure 2: The synthetic fitting dataset (left) and cost landscape (right) for an example time-series battery model parameterisation using a root-mean-squared error cost function.**
*(左图显示了带噪声的电压与时间的关系曲线。右图是一个二维成本等高线图，坐标轴为负极颗粒扩散系数和接触电阻，图中标记了初始值和真实值。)*
***

We can also use PyBOP's to generate and fit electrochemical impedance data using methods within pybamm-eis that enable fast impedance computation of battery models (Dhoot et al., 2024). Using the same model and parameters as in the time-domain case, Figure 3 shows the numerical impedance prediction available in PyBOP alongside the cost landscape for the corresponding inference task. At the time of publication, gradient-based optimisation and sampling methods are not available when using an impedance workflow.

***
**Figure 3: The data and model fit (left) and cost landscape (right) for a frequency-domain impedance parameterisation with a root-mean-squared error cost function, at 5% SOC.**
*(左图是一个Nyquist图，比较了模型和参考数据的阻抗。右图是一个二维成本等高线图，坐标轴与图2相同，用于阻抗拟合任务。)*
***

To avoid confusion, we continue with identification in the time-domain (Figure 2). In general, however, time- and frequency-domain models and data may be combined for improved parameterisation. As gradient information is available for our time-domain example, the choice of distance-based cost function and optimiser is not constrained. Due to the difference in magnitude between the two parameters, we apply the logarithmic parameter transformation offered by PyBOP. This transforms the search space of the optimiser to allow for a common step size between the parameters, improving convergence in this particular case. As a demonstration of the parameterisation capabilities of PyBOP, Figure 4 (left) shows the rate of convergence for each of the distance-minimising cost functions, while Figure 4 (right) shows analogous results for maximising a likelihood. The optimisation is performed with SciPy Minimize using the gradient-based L-BFGS-B method.

***
**Figure 4: Optimiser convergence using various cost (left) and likelihood (right) functions and the L-BFGS-B algorithm.**
*(左图显示了不同成本函数（Minkowski, Sum of Power, Sum Squared Error, Root Mean Squared Error）下成本随迭代次数的收敛情况。右图显示了不同高斯对数似然函数下似然值随迭代次数的收敛情况。)*
***

Using the same model and parameters, we compare example convergence rates of various algorithms across several categories: gradient-based methods in Figure 5 (left), evolutionary strategies in Figure 5 (middle) and (meta)heuristics in Figure 5 (right) using a mean-squared-error cost function.

***
**Figure 5: Convergence in parameter values for several optimisation algorithms provided by PyBOP.**
*(该图由三组图表组成，每组包括两个图，分别显示负极颗粒扩散系数和接触电阻随函数调用次数的收敛情况。左侧图组展示了基于梯度的算法（AdamW, iRprop-, Gradient descent, SciPy Minimize）。中间图组展示了进化策略算法（CMA-ES, xNES, sNES, SciPy Differential Evolution）。右侧图组展示了（元）启发式算法（PSO, Nelder-Mead, Cuckoo Search）。)*
***

We also show the cost function landscapes alongside optimiser iterations in Figure 6, with the three rows showing the gradient-based optimisers (top), evolution strategies (middle), and (meta)heuristics (bottom). Note that the performance of the optimiser depends on the cost landscape, the initial guess or prior, and the hyperparameters for each specific problem.

***
**Figure 6: Cost landscape contour plot with corresponding optimisation traces, for several optimisers.**
*(该图展示了一系列成本等高线图，每个图上都叠加了特定优化算法的优化轨迹。这些算法包括AdamW, IRPropMin, GradientDescent, SciPyMinimize, CMAES, XNES, SNES, SciPyDifferentialEvolution, PSO, NelderMead, 和 CuckooSearch。)*
***

This example parameterisation task can also be approached from a Bayesian perspective, using PyBOP's sampler methods. First, we introduce Bayes' rule,
```
P(θ|D) = [P(D|θ)P(θ)] / P(D),    (6)
```
where P(θ|D) is the posterior parameter distribution, P(D|θ) is the likelihood function, P(θ) is the prior parameter distribution, and P(D) is the model evidence, or marginal likelihood, which acts as a normalising constant. In the case of maximum likelihood estimation or maximum a posteriori estimation, one wishes to maximise P(D|θ) or P(θ|D), respectively, and this may be formulated as an optimisation problem as per Equation 5.

One must use sampling or other inference methods to reconstruct the full posterior parameter distribution, P(θ|D). The posterior distribution provides information about the uncertainty of the identified parameters, e.g., by calculating the variance or other moments. Monte Carlo methods are used here to sample from the posterior. The selection of Monte Carlo methods available in PyBOP includes gradient-based methods such as no-u-turn (Hoffman & Gelman, 2011) and Hamiltonian (Brooks et al., 2011), as well as heuristic methods such as differential evolution (Braak, 2006), and also conventional methods based on random sampling with rejection criteria (Metropolis et al., 1953). PyBOP offers a sampler class that provides the interface to samplers, the latter being provided by the probabilistic inference on noisy time-series (PINTS) package. Figure 7 shows the sampled posteriors for the synthetic model described previously, using an adaptive covariance-based sampler called Haario Bardenet (Haario et al., 2001).

***
**Figure 7: Posterior distributions of model parameters alongside identified noise on the observations. Shaded areas denote the 95th percentile credible interval for each parameter.**
*(该图包含三个直方图，分别显示了负极颗粒半径、接触电阻和观测噪声的后验分布。)*
***

### Design optimisation

Design optimisation is supported in PyBOP to guide device design development by identifying parameter sensitivities that can unlock improvements in performance. This problem can be viewed in a similar way to the parameterisation workflows described previously, but with the aim of maximising a design-objective cost function rather than minimising a distance-based cost function. PyBOP performs maximisation by minimising the negative of the cost function. In design problems, the cost metric is no longer a distance between two time series, but a metric evaluated on a model prediction. For example, to maximise the gravimetric energy (or power) density, the cost is the integral of the discharge energy (or power) normalised by the cell mass. Such metrics are typically quantified for operating conditions such as a 1C discharge, at a given temperature. In general, design optimisation can be written as a constrained optimisation problem,
```
min L(θ) subject to equations (1)-(4), θ ∈ Ω    (7)
```
where L : θ → [0, ∞) is a cost function that quantifies the desirability of the design and Ω is the set of allowable parameter values.

As an example, we consider the challenge of maximising the gravimetric energy density, subject to constraints on two of the geometric electrode parameters (Couto et al., 2023). In this case we use the PyBaMM implementation of the single particle model with electrolyte (SPMe) to investigate the impact of the positive electrode thickness and the active material volume fraction on the energy density. Since the total volume fraction must sum to unity, the positive electrode porosity for each optimisation iteration is defined in relation to the active material volume fraction. It is also possible to update the 1C rate corresponding to the theoretical capacity for each iteration of the design.

***
**Figure 8: Initial and optimised voltage profiles alongside the gravimetric energy density cost landscape.**
*(左图比较了优化前后的电压放电曲线，显示了优化后的性能提升。右图是一个二维成本等高线图，坐标轴为正极厚度和正极活性材料体积分数，图中显示了Nelder-Mead搜索的优化轨迹。)*
***

Figure 8 (left) shows the predicted improvement in the discharge profile between the initial and optimised parameter values for a fixed-rate 1C discharge selected from the initial design and (right) the Nelder-Mead search over the parameter space.

## Conclusion

In this article, the open-source parameter identification and optimisation package, PyBOP has been introduced. This package provides specialised classes and methods to support both physics-based and data-driven parameter identification and optimisation. An example identification workflow has been presented using both point-estimate and distribution-based inference methods for both frequency and time domain forward model predictions. An additional workflow for design optimisation was also presented, showcasing PyBOP as a versatile package capable for researchers and engineers.

## Acknowledgements

We gratefully acknowledge all contributors to PyBOP. This work was supported by the Faraday Institution Multiscale Modelling project (ref. FIRG059), UKRI's Horizon Europe Guarantee (ref. 10038031), and EU IntelLiGent project (ref. 101069765).

## References

*   Aitio, A., Marquis, S. G., Ascencio, P., & Howey, D. (2020). Bayesian parameter estimation applied to the li-ion battery single particle model with electrolyte dynamics *IFAC-PapersOnLine, 53*(2), 12497-12504. https://doi.org/10.1016/j.ifacol.2020.12.1770
*   Andersson, M., Streb, M., Ko, J. Y., Löfqvist Klass, V., Klett, M., Ekström, H., Johansson, M., & Lindbergh, G. (2022). Parametrization of physics-based battery models from input-output data: A review of methodology and current research. *Journal of Power Sources, 521* (November 2021), 230859. https://doi.org/10.1016/j.jpowsour.2021.230859
*   Braak, C. J. F. T. (2006). A Markov Chain Monte Carlo version of the genetic algorithm Differential Evolution: Easy Bayesian computing for real parameter spaces. *Statistics and Computing, 16*(3), 239–249. https://doi.org/10.1007/s11222-006-8769-1
*   ... and other references from the document..